[project]
# Whether to enable telemetry (default: true). No personal data is collected.
enable_telemetry = true

# List of environment variables to be provided by each user to use the app.
user_env = []

# Duration (in seconds) during which the session is saved when the connection is lost
session_timeout = 3600

# Enable third parties caching (e.g Lang<PERSON>hain cache)
cache = false

# Authorized origins
allow_origins = ["*"]

# Follow symlink for asset mount (see https://github.com/Chainlit/chainlit/issues/317)
# follow_symlink = false

[features]
# Show the prompt playground
prompt_playground = true

# Process and display HTML in messages. This can be a security risk (see https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript)
unsafe_allow_html = false

# Process and display mathematical expressions. This can clash with "$" characters in messages.
latex = false

# Automatically tag threads when a user takes an action (useful for analytics)
auto_tag_thread = true

# Literal values to use for auto-tagging threads
auto_tag_thread_values = []

[UI]
# Name of the app and chatbot.
name = "RAG Chat Med 🏥"

# Show the readme while the thread is empty.
show_readme_as_default = true

# Description of the app and chatbot. This is used for HTML tags.
description = "Chat inteligente com documentos médicos usando IA local"

# Large size content are by default collapsed for a cleaner ui
default_collapse_content = true

# The default value for the expand messages settings.
default_expand_messages = false

# Hide the chain of thought details from the user in the UI.
hide_cot = false

# Link to your github repo. This will add a github button in the UI's header.
# github = ""

# Specify a CSS file that can be used to customize the user interface.
# The CSS file can be served from the public directory or via an external link.
custom_css = "/public/style.css"

# Specify a Javascript file that can be used to customize the user interface.
# The Javascript file can be served from the public directory or via an external link.
custom_js = "/public/script.js"

# Override default MUI light theme. (Check theme.ts)
[UI.theme]
primary_color = "#2E7D32"
background_color = "#F8F9FA"
paper_color = "#FFFFFF"

[UI.theme.light]
background = "#F8F9FA"
paper = "#FFFFFF"
primary = { main = "#2E7D32", dark = "#1B5E20", light = "#66BB6A" }
secondary = { main = "#1976D2", dark = "#0D47A1", light = "#42A5F5" }
error = { main = "#D32F2F", dark = "#C62828", light = "#EF5350" }
warning = { main = "#ED6C02", dark = "#E65100", light = "#FF9800" }
info = { main = "#0288D1", dark = "#01579B", light = "#03DAC6" }
success = { main = "#2E7D32", dark = "#1B5E20", light = "#4CAF50" }
text = { primary = "#212121", secondary = "#757575" }

[UI.theme.dark]
background = "#0F1419"
paper = "#1E1E1E"
primary = { main = "#66BB6A", dark = "#388E3C", light = "#A5D6A7" }
secondary = { main = "#42A5F5", dark = "#1976D2", light = "#90CAF9" }
error = { main = "#EF5350", dark = "#D32F2F", light = "#FFCDD2" }
warning = { main = "#FF9800", dark = "#F57C00", light = "#FFE0B2" }
info = { main = "#29B6F6", dark = "#0277BD", light = "#B3E5FC" }
success = { main = "#66BB6A", dark = "#388E3C", light = "#C8E6C9" }
text = { primary = "#FFFFFF", secondary = "#B0BEC5" }

[meta]
generated_by = "1.0.0"
