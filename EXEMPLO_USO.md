# 📋 Exemplo de Uso - RAG Chat Med

## 🚀 Como Testar a Aplicação

### 1. Iniciar a Aplicação
```bash
chainlit run app.py
```

### 2. Exemplos de Perguntas para Documentos Médicos

#### Para Prontuários Médicos:
- "Qual é o diagnóstico principal do paciente?"
- "Quais sintomas foram relatados?"
- "Que medicamentos foram prescritos?"
- "Há alguma alergia mencionada?"
- "Qual é o histórico médico do paciente?"

#### Para Relatórios de Exames:
- "Quais são os resultados dos exames?"
- "Há alguma alteração significativa?"
- "Os valores estão dentro da normalidade?"
- "Que exames foram solicitados?"
- "Qual é a conclusão do relatório?"

#### Para Artigos Científicos:
- "Qual é o objetivo do estudo?"
- "Quais foram os métodos utilizados?"
- "Quais são as principais conclusões?"
- "Há limitações mencionadas?"
- "Que recomendações são feitas?"

### 3. Funcionalidades Visuais para Testar

#### Drag & Drop:
- Arraste um arquivo PDF diretamente para a janela do navegador
- Observe o feedback visual durante o arraste

#### Atalhos de Teclado:
- **Ctrl + Enter**: Enviar mensagem
- **Escape**: Limpar campo de entrada

#### Animações:
- Observe as animações suaves ao enviar mensagens
- Veja o indicador de "digitando" durante o processamento
- Note as transições suaves entre estados

#### Responsividade:
- Teste em diferentes tamanhos de tela
- Verifique a adaptação mobile

### 4. Tipos de Documentos Recomendados para Teste

#### ✅ Funcionam Bem:
- Prontuários médicos em PDF
- Relatórios de laboratório
- Artigos científicos médicos
- Protocolos clínicos
- Resumos de alta hospitalar

#### ⚠️ Limitações:
- PDFs com muitas imagens (texto pode não ser extraído)
- Documentos escaneados de baixa qualidade
- PDFs protegidos por senha
- Arquivos muito grandes (>100MB)

### 5. Dicas de Uso

#### Para Melhores Resultados:
1. **Seja Específico**: Faça perguntas claras e diretas
2. **Use Contexto**: Referencie informações já discutidas
3. **Explore Gradualmente**: Comece com perguntas gerais, depois específicas
4. **Verifique Fontes**: Sempre confira as referências fornecidas

#### Exemplos de Conversas:
```
Usuário: "Qual é o diagnóstico principal?"
IA: "Baseado no documento, o diagnóstico principal é..."

Usuário: "Que tratamento foi recomendado para isso?"
IA: "Para esse diagnóstico, o documento menciona..."

Usuário: "Há algum efeito colateral mencionado?"
IA: "Sim, o documento indica os seguintes efeitos..."
```

### 6. Solução de Problemas

#### Se o processamento falhar:
- Verifique se o Ollama está rodando
- Confirme se os modelos estão instalados
- Tente com um PDF menor

#### Se as respostas não forem precisas:
- Reformule a pergunta de forma mais específica
- Verifique se o conteúdo está no documento
- Tente perguntas sobre seções específicas

#### Se a interface não carregar corretamente:
- Limpe o cache do navegador
- Verifique se os arquivos CSS/JS estão acessíveis
- Reinicie a aplicação

### 7. Personalização Adicional

#### Para Desenvolvedores:
- Modifique `public/style.css` para ajustar cores/layout
- Edite `public/script.js` para adicionar funcionalidades
- Ajuste `.chainlit/config.toml` para configurações avançadas
- Personalize `chainlit.md` para mudar a tela de boas-vindas

---

## 🎯 Próximos Passos

Após testar a aplicação, considere:
- Experimentar com diferentes tipos de documentos
- Personalizar ainda mais a interface
- Integrar com outros modelos de IA
- Adicionar funcionalidades específicas para seu caso de uso
