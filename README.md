# 🏥 RAG Chat Med - Assistente Médico Inteligente

Uma interface moderna e intuitiva para análise inteligente de documentos médicos usando IA local com Ollama e LangChain.

## ✨ Características Principais

- 🎨 **Interface Moderna**: Design responsivo com tema médico profissional
- 🔒 **100% Local**: Processamento seguro sem envio de dados para nuvem
- 🧠 **IA Conversacional**: Memória de contexto para conversas naturais
- 📚 **Referências Precisas**: Cada resposta inclui fontes do documento
- ⚡ **Performance Otimizada**: Processamento rápido com embeddings locais
- 🌙 **Tema Adaptável**: Suporte a modo claro e escuro
- 📱 **Responsivo**: Funciona perfeitamente em desktop e mobile

## 🚀 Melhorias Visuais Implementadas

### Interface Aprimorada
- Gradientes modernos em verde médico
- Animações suaves para melhor UX
- Mensagens com design de chat moderno
- Indicadores visuais de processamento
- Scrollbar personalizada

### Funcionalidades Visuais
- Drag & drop para upload de arquivos
- Animações de entrada para mensagens
- Feedback visual durante processamento
- Botões com efeitos hover e pulse
- Atalhos de teclado (Ctrl+Enter, Escape)

### Personalização
- CSS customizado com tema médico
- JavaScript para interações avançadas
- Configuração Chainlit otimizada
- Tela de boas-vindas informativa

## 🛠️ Tecnologias Utilizadas

- **Frontend**: Chainlit com CSS/JS customizado
- **Backend**: LangChain + Ollama
- **Embeddings**: Nomic Embed Text (local)
- **Banco Vetorial**: ChromaDB
- **Processamento PDF**: PyPDF2

## 📋 Pré-requisitos

- Python 3.8+
- Ollama instalado e rodando
- Modelos necessários:
  - `tinyllama:latest`
  - `nomic-embed-text`

## 🔧 Instalação

1. Clone o repositório
2. Instale as dependências: `pip install -r requirements.txt`
3. Execute: `chainlit run app.py`

## 🎯 Como Usar

1. **Upload**: Faça upload de um PDF médico
2. **Processamento**: Aguarde a IA indexar o documento
3. **Conversa**: Faça perguntas sobre o conteúdo
4. **Análise**: Receba respostas com referências precisas
