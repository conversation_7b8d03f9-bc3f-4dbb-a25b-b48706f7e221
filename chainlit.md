# 🏥 RAG Chat Med - Assistente Médico Inteligente

Bem-vindo ao **RAG Chat Med**, sua ferramenta de IA para análise inteligente de documentos médicos! 🩺✨

## 🚀 Como Funciona

1. **📄 Upload do Documento**: Faça upload de um arquivo PDF médico (prontuários, artigos, relatórios)
2. **🧠 Processamento IA**: Nossa IA processa e indexa o conteúdo usando embeddings locais
3. **💬 Chat Inteligente**: Faça perguntas sobre o documento e receba respostas precisas com referências

## ✨ Características Principais

- 🔒 **100% Local**: Seus dados permanecem seguros em sua máquina
- 🎯 **Respostas Precisas**: Baseadas no conteúdo específico do seu documento
- 📚 **Referências**: Cada resposta inclui as fontes do documento
- 🧠 **Memória Conversacional**: Mantém o contexto durante toda a conversa
- ⚡ **Rápido e Eficiente**: Processamento otimizado com Ollama

## 🔧 Tecnologias Utilizadas

- **LangChain**: Framework para aplicações de IA
- **Ollama**: Modelos de linguagem locais
- **ChromaDB**: Banco de dados vetorial
- **Chainlit**: Interface de chat moderna

## 📋 Tipos de Documentos Suportados

- 📋 Prontuários médicos
- 📊 Relatórios de exames
- 📖 Artigos científicos
- 📝 Protocolos clínicos
- 🏥 Documentos hospitalares

## 🎨 Interface Moderna

Nossa interface foi cuidadosamente projetada para oferecer:
- 🎨 Design moderno e intuitivo
- 📱 Responsivo para todos os dispositivos
- 🌙 Suporte a tema claro e escuro
- ⚡ Animações suaves e feedback visual

---

### 🚀 Pronto para começar?

Clique no botão de upload acima e envie seu primeiro documento PDF para começar a conversar com sua IA médica pessoal!
