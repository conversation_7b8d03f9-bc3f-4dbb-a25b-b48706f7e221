// Custom JavaScript for RAG Chat Med

// Add smooth scrolling behavior
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scroll to new messages
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if new message was added
                const newNodes = Array.from(mutation.addedNodes);
                const hasNewMessage = newNodes.some(node => 
                    node.nodeType === Node.ELEMENT_NODE && 
                    (node.classList?.contains('message') || 
                     node.querySelector?.('.message'))
                );
                
                if (hasNewMessage) {
                    // Smooth scroll to bottom
                    setTimeout(() => {
                        window.scrollTo({
                            top: document.body.scrollHeight,
                            behavior: 'smooth'
                        });
                    }, 100);
                }
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Add typing indicator animation
    function addTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;
        return typingDiv;
    }

    // Enhanced file upload feedback
    document.addEventListener('dragover', function(e) {
        e.preventDefault();
        document.body.classList.add('drag-over');
    });

    document.addEventListener('dragleave', function(e) {
        e.preventDefault();
        if (!document.body.contains(e.relatedTarget)) {
            document.body.classList.remove('drag-over');
        }
    });

    document.addEventListener('drop', function(e) {
        e.preventDefault();
        document.body.classList.remove('drag-over');
    });

    // Add welcome animation
    const welcomeElements = document.querySelectorAll('.welcome-screen *');
    welcomeElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Add pulse effect to important buttons
    function addPulseEffect(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                this.style.animation = 'pulse 0.6s ease-in-out';
            });
            
            element.addEventListener('animationend', function() {
                this.style.animation = '';
            });
        });
    }

    // Apply pulse effect to buttons
    addPulseEffect('.MuiButton-root');
    addPulseEffect('.MuiIconButton-root');

    // Add loading state management
    let isProcessing = false;
    
    function setProcessingState(processing) {
        isProcessing = processing;
        const sendButton = document.querySelector('[type="submit"]');
        const inputField = document.querySelector('input[type="text"]');
        
        if (sendButton) {
            sendButton.disabled = processing;
            if (processing) {
                sendButton.classList.add('processing');
            } else {
                sendButton.classList.remove('processing');
            }
        }
        
        if (inputField) {
            inputField.disabled = processing;
        }
    }

    // Monitor for processing states
    const processingObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const addedNodes = Array.from(mutation.addedNodes);
                const hasProcessingMessage = addedNodes.some(node =>
                    node.textContent?.includes('Processando') ||
                    node.textContent?.includes('Processing')
                );
                
                if (hasProcessingMessage) {
                    setProcessingState(true);
                }
                
                const hasCompletedMessage = addedNodes.some(node =>
                    node.textContent?.includes('concluído') ||
                    node.textContent?.includes('done')
                );
                
                if (hasCompletedMessage) {
                    setProcessingState(false);
                }
            }
        });
    });

    processingObserver.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + Enter to send message
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const sendButton = document.querySelector('[type="submit"]');
            if (sendButton && !isProcessing) {
                sendButton.click();
            }
        }
        
        // Escape to clear input
        if (e.key === 'Escape') {
            const inputField = document.querySelector('input[type="text"]');
            if (inputField) {
                inputField.value = '';
                inputField.focus();
            }
        }
    });

    // Add copy functionality to code blocks
    function addCopyButtons() {
        const codeBlocks = document.querySelectorAll('pre, code');
        codeBlocks.forEach(block => {
            if (block.textContent.length > 50) { // Only add to longer code blocks
                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.innerHTML = '📋';
                copyButton.title = 'Copiar código';
                
                copyButton.addEventListener('click', function() {
                    navigator.clipboard.writeText(block.textContent).then(() => {
                        copyButton.innerHTML = '✅';
                        setTimeout(() => {
                            copyButton.innerHTML = '📋';
                        }, 2000);
                    });
                });
                
                block.style.position = 'relative';
                block.appendChild(copyButton);
            }
        });
    }

    // Run copy button addition periodically for new content
    setInterval(addCopyButtons, 2000);

    console.log('🏥 RAG Chat Med - Interface aprimorada carregada com sucesso!');
});
