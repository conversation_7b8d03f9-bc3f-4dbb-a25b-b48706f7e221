/* Custom CSS for RAG Chat Med */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Global styles */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Ensure proper box-sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Main container styling */
.MuiContainer-root {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Header styling */
.MuiAppBar-root {
  background: linear-gradient(90deg, #2E7D32 0%, #388E3C 100%) !important;
  box-shadow: 0 4px 20px rgba(46, 125, 50, 0.3) !important;
}

/* Chat container */
.MuiPaper-root {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Message bubbles */
.message-container {
  margin: 12px 0;
}

/* User messages */
.user-message {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%) !important;
  color: white !important;
  border-radius: 18px 18px 4px 18px !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3) !important;
  margin-left: auto !important;
  max-width: 80% !important;
}

/* Assistant messages */
.assistant-message {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 18px 18px 18px 4px !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  max-width: 80% !important;
}

/* Input field styling */
.MuiOutlinedInput-root {
  border-radius: 25px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
}

.MuiOutlinedInput-root:hover {
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

.MuiOutlinedInput-root.Mui-focused {
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0 4px 20px rgba(46, 125, 50, 0.2) !important;
}

/* Send button */
.MuiIconButton-root {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%) !important;
  color: white !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
}

.MuiIconButton-root:hover {
  background: linear-gradient(135deg, #1B5E20 0%, #388E3C 100%) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4) !important;
}

/* File upload area */
.file-upload-area {
  border: 2px dashed #2E7D32 !important;
  border-radius: 16px !important;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%) !important;
  padding: 32px !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
}

.file-upload-area:hover {
  border-color: #4CAF50 !important;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%) !important;
  transform: translateY(-2px) !important;
}

/* Loading animation */
.loading-spinner {
  border: 3px solid rgba(46, 125, 50, 0.3);
  border-top: 3px solid #2E7D32;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Source documents styling */
.source-document {
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%) !important;
  border: 1px solid #C8E6C9 !important;
  border-radius: 12px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
  transition: all 0.3s ease !important;
}

.source-document:hover {
  background: linear-gradient(135deg, #C8E6C9 0%, #DCEDC8 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.2) !important;
}

/* Welcome screen enhancements */
.welcome-screen {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  margin: 20px 0;
}

.welcome-title {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1B5E20 0%, #388E3C 100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .user-message,
  .assistant-message {
    max-width: 95% !important;
  }
  
  .welcome-title {
    font-size: 2rem !important;
  }
}

/* Animation for new messages */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-container {
  animation: slideInUp 0.3s ease-out;
}

/* Pulse animation for processing */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.processing {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 8px 0;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #2E7D32;
  animation: typing 1.4s ease-in-out infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Drag and drop styling */
body.drag-over {
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%) !important;
}

body.drag-over::after {
  content: "📄 Solte o arquivo PDF aqui!";
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(46, 125, 50, 0.9);
  color: white;
  padding: 20px 40px;
  border-radius: 16px;
  font-size: 1.2rem;
  font-weight: 600;
  z-index: 9999;
  pointer-events: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Copy button for code blocks */
.copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(46, 125, 50, 0.8);
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.copy-button:hover {
  background: rgba(46, 125, 50, 1);
  transform: scale(1.1);
}

/* Enhanced button styles */
.MuiButton-root {
  border-radius: 25px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  padding: 10px 24px !important;
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3) !important;
  transition: all 0.3s ease !important;
}

.MuiButton-root:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4) !important;
}
